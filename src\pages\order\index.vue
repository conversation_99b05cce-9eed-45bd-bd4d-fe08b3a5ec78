<template>
  <div class="order-list-page">
    <!-- 页面头部 -->
    <van-sticky>
      <div class="page-header">
        <div class="page-title">订单管理</div>
        <div class="header-actions">
          <van-button
            icon="plus"
            type="danger"
            size="small"
            round
            @click="onCreate"
            >新订单</van-button
          >
          <van-icon name="search" size="20" @click="showSearchPanel = true" />
        </div>
      </div>
    </van-sticky>

    <!-- 搜索面板 -->
    <van-popup
      v-model:show="showSearchPanel"
      position="bottom"
      :style="{ height: '70%' }"
      round
      closeable
      close-icon="close"
      @closed="onSearchPanelClosed"
    >
      <div class="search-panel">
        <div class="search-panel-header">
          <span class="title">搜索条件</span>
        </div>

        <van-form @submit="onSearch" class="search-form">
          <van-cell-group inset>
            <van-field
              v-model="formInline.id"
              label="订单号"
              placeholder="请输入订单号"
              clearable
            >
              <template #right-icon>
                <van-icon
                  name="clear"
                  v-if="formInline.id"
                  @click="formInline.id = ''"
                />
              </template>
            </van-field>

            <van-field
              v-model="formInline.materielCode"
              label="型号/零件名称/编号"
              placeholder="请输入型号/零件名称/编号"
              clearable
            >
              <template #right-icon>
                <van-icon
                  name="clear"
                  v-if="formInline.materielCode"
                  @click="formInline.materielCode = ''"
                />
              </template>
            </van-field>

            <van-field
              readonly
              label="创建日期"
              placeholder="请选择日期范围"
              @click="showDatePicker = true"
              :modelValue="dateRangeText"
            >
              <template #right-icon>
                <van-icon
                  name="clear"
                  v-if="dateRangeText"
                  @click.stop="clearDateRange"
                />
                <van-icon name="calendar-o" v-else />
              </template>
            </van-field>
          </van-cell-group>

          <div class="form-buttons">
            <van-button
              type="default"
              block
              @click="resetSearch"
              :disabled="!hasActiveFilters"
              >重置</van-button
            >
            <van-button type="danger" block native-type="submit"
              >筛选</van-button
            >
          </div>
        </van-form>
      </div>
    </van-popup>

    <!-- 日期选择器弹出层 -->
    <van-calendar
      v-model:show="showDatePicker"
      type="range"
      title="选择日期范围"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="onDateConfirm"
      :default-date="
        formInline.createDate.length > 0 ? formInline.createDate : null
      "
      color="#ee0a24"
    />

    <!-- 创建新订单按钮 -->
    <!-- <div class="create-order-btn">
      <van-button type="danger" icon="plus" block @click="onCreate">生成新订单</van-button>
    </div> -->

    <!-- 订单状态标签页 -->
    <van-tabs
      v-model:active="activeName"
      @change="handleTabClick"
      sticky
      swipeable
      animated
    >
      <van-tab
        v-for="(item, key) in listUnion"
        :key="key"
        :title="item.label"
        :name="key"
      >
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <van-list
            v-model:loading="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad"
          >
            <!-- 订单列表 -->
            <div class="order-list">
              <div
                class="order-item"
                v-for="(order, index) in item.list"
                :key="index"
              >
                <!-- 订单头部信息 -->
                <div class="order-header">
                  <div class="order-status">
                    <van-tag type="warning" v-if="order.orderStatus === '1'"
                      >待确认</van-tag
                    >
                    <van-tag type="warning" v-if="order.orderStatus === '20'"
                      >未发货</van-tag
                    >
                    <van-tag type="danger" v-if="order.orderStatus === '30'"
                      >待取件</van-tag
                    >
                    <van-tag type="primary" v-if="order.orderStatus === '1000'"
                      >部分发货</van-tag
                    >
                    <van-tag type="success" v-if="order.orderStatus === '1001'"
                      >已发货</van-tag
                    >
                  </div>
                  <div class="order-date">
                    {{ dayjs(order.createDate).format("MM/DD/YYYY") }}
                  </div>
                </div>

                <!-- 订单信息 -->
                <div class="order-info">
                  <div class="order-id">
                    <van-icon name="orders-o" />
                    <span>订单号: {{ order.id }}</span>
                  </div>

                  <div class="order-price">
                    <span class="price-label">订单金额:</span>
                    <span class="price-value">
                      {{
                        userInfo.currency === "1"
                          ? "¥"
                          : userInfo.currency === "2"
                          ? "$"
                          : userInfo.currency === "3"
                          ? "€"
                          : ""
                      }}
                      {{ apiService.utils.formatPrice(order.lastMoney) }}
                    </span>
                  </div>
                </div>

                <!-- 物流信息 -->
                <div
                  class="logistics-info"
                  v-if="
                    order.outWarehouseList && order.outWarehouseList.length > 0
                  "
                >
                  <div
                    v-for="(item, idx) in order.outWarehouseList"
                    :key="idx"
                    class="logistics-item"
                  >
                    <div class="logistics-company">
                      <van-icon name="logistics" />
                      <span>{{ item.logisticsCompany }}</span>
                    </div>
                    <div
                      class="logistics-number"
                      @click="
                        downloadPackingListExcel(item.logisticsNumber, order.id)
                      "
                    >
                      <span>{{ item.logisticsNumber }}</span>
                      <van-icon name="down" />
                    </div>
                  </div>
                </div>

                <!-- 订单操作按钮 -->
                <div class="order-actions">
                  <van-button
                    v-if="order.orderStatus === '1'"
                    type="primary"
                    size="small"
                    @click="showConfirmDialog(order.id)"
                    >确认</van-button
                  >

                  <van-button type="primary" size="small" @click="edit(order)"
                    >详情</van-button
                  >

                  <van-button
                    v-if="isWithin30Min(order.createDate)"
                    type="danger"
                    size="small"
                    @click="showDeleteDialog(order.id)"
                    >删除</van-button
                  >

                  <van-button
                    icon="description"
                    size="small"
                    plain
                    @click="downloadOrderDetailExcel(order.id)"
                    >导出</van-button
                  >

                  <van-button
                    v-if="
                      order.orderStatus == '1001' || order.orderStatus == '1000'
                    "
                    icon="bill"
                    type="warning"
                    size="small"
                    plain
                    @click="downloadInvoicePdf(order.id)"
                    >发票</van-button
                  >
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <van-empty
              v-if="item.list.length === 0 && !loading"
              description="暂无订单"
            />
          </van-list>
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>

  <!-- 订单详情弹窗 -->
  <van-popup
    v-model:show="detailVisible"
    position="right"
    :style="{ width: '100%', height: '100%' }"
  >
    <div class="detail-header">
      <div class="title">订单详情</div>
      <van-icon name="cross" @click="detailVisible = false" />
    </div>
    <OrderDetail :params="currentOrder" @close="detailVisible = false" />
  </van-popup>

  <!-- 确认对话框 -->
  <van-dialog
    v-model:show="confirmDialogVisible"
    title="确认订单"
    show-cancel-button
    @confirm="confirmOrderAction"
  >
    确定要确认此订单吗？
  </van-dialog>

  <!-- 删除对话框 -->
  <van-dialog
    v-model:show="deleteDialogVisible"
    title="删除订单"
    show-cancel-button
    @confirm="deleteOrderAction"
  >
    确定要删除此订单吗？
  </van-dialog>
</template>

<script setup>
import { ref, onMounted, inject, computed } from "vue";
import { useRouter } from "vue-router";
import { showToast, showSuccessToast } from "vant";
import dayjs from "dayjs";
import OrderDetail from "./components/OrderDetail.vue";
import apiService from "@/utils/api";
import { previewFileForMobile } from "@/utils/mobileDownload";

// 基础配置
const base_downFileByPath_url = inject("$base_downFileByPath_url");
const router = useRouter();
const userInfo = ref(JSON.parse(localStorage.getItem("userInfo") || "{}"));

// 状态变量
const activeName = ref("allOrders");
const loading = ref(false);
const finished = ref(false);
const refreshing = ref(false);
const showDatePicker = ref(false);
const showSearchPanel = ref(false);
const detailVisible = ref(false);
const confirmDialogVisible = ref(false);
const deleteDialogVisible = ref(false);
const currentOrderId = ref("");
const currentOrder = ref({ id: "" });
const searchApplied = ref(false);

// 日期选择器配置
const minDate = new Date(new Date().getFullYear() - 1, 0, 1);
const maxDate = new Date();

// 表单数据
const formInline = ref({
  id: "",
  materielCode: "",
  createDate: [],
});

// 日期范围文本
const dateRangeText = ref("");

// 计算属性：是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return (
    formInline.value.id || formInline.value.materielCode || dateRangeText.value
  );
});

// 清除日期范围
const clearDateRange = () => {
  formInline.value.createDate = [];
  dateRangeText.value = "";
};

// 搜索面板关闭回调
const onSearchPanelClosed = () => {
  // 如果有活跃的筛选条件但未应用，可以在这里处理
};

// 订单列表数据
const listUnion = ref({
  allOrders: {
    label: "全部",
    orderStatus: undefined,
    pages: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    list: [],
  },
  Unfulfilled: {
    label: "未发货",
    orderStatus: "20",
    pages: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    list: [],
  },
  Waiting: {
    label: "待取件",
    orderStatus: "30",
    pages: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    list: [],
  },
  Partially: {
    label: "部分发货",
    orderStatus: "1000",
    pages: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    list: [],
  },
  Completed: {
    label: "已发货",
    orderStatus: "1001",
    pages: {
      current: 1,
      pageSize: 10,
      total: 0,
    },
    list: [],
  },
});

// 检查是否在30分钟内
const isWithin30Min = (dateString) => {
  const currentDate = new Date();
  const givenDate = new Date(dateString);
  const timeDifference = currentDate.getTime() - givenDate.getTime();
  return timeDifference <= 0.5 * 60 * 60 * 1000;
};

// 获取当前标签页数据
const getTabData = () => {
  return listUnion.value[activeName.value];
};

// 获取订单列表
const getList = (isLoadMore = false) => {
  const {
    pages: { current, pageSize },
    orderStatus,
  } = listUnion.value[activeName.value];

  loading.value = true;

  const [startTime, endTime] = formInline.value.createDate || [];
  const params = {
    id: formInline.value.id || "",
    materielCode: formInline.value.materielCode || "",
    orderStatus: orderStatus || "",
    pageNo: current,
    pageSize: pageSize,
  };

  if (startTime && endTime) {
    params.beginCreateDate = dayjs(startTime).format("YYYY-MM-DD");
    params.endCreateDate = dayjs(endTime).format("YYYY-MM-DD");
  }

  apiService.order
    .getOrderListPage(params)
    .then((result) => {
      if (refreshing.value) {
        listUnion.value[activeName.value].list = [];
        refreshing.value = false;
      }

      const newList = result.data || [];

      if (isLoadMore) {
        // 追加数据
        listUnion.value[activeName.value].list = [
          ...listUnion.value[activeName.value].list,
          ...newList,
        ];
      } else {
        // 替换数据
        listUnion.value[activeName.value].list = newList;
      }

      // 更新分页信息
      listUnion.value[activeName.value].pages.current = result.pageNo;
      listUnion.value[activeName.value].pages.total = result.total;

      // 判断是否加载完成
      if (
        listUnion.value[activeName.value].list.length >= result.total ||
        newList.length < pageSize
      ) {
        finished.value = true;
      } else {
        finished.value = false;
      }
    })
    .catch((error) => {
      showToast("获取订单列表失败");
    })
    .finally(() => {
      loading.value = false;
    });
};

// 日期选择确认
const onDateConfirm = (date) => {
  formInline.value.createDate = date;
  dateRangeText.value = `${dayjs(date[0]).format("YYYY-MM-DD")} 至 ${dayjs(
    date[1]
  ).format("YYYY-MM-DD")}`;
  showDatePicker.value = false;
};

// 重置搜索条件
const resetSearch = () => {
  formInline.value = {
    id: "",
    materielCode: "",
    createDate: [],
  };
  dateRangeText.value = "";
  searchApplied.value = false;
};

// 下拉刷新
const onRefresh = () => {
  refreshing.value = true;
  finished.value = false;
  getTabData().pages.current = 1;
  getList();
};

// 上拉加载更多
const onLoad = () => {
  const currentTab = getTabData();

  // 如果已经加载完成，不再请求
  if (finished.value) return;

  // 增加页码，加载下一页
  currentTab.pages.current += 1;
  getList(true);
};

// 页面加载时获取数据
onMounted(() => {
  getList();
});

// 创建新订单
const onCreate = () => {
  router.push("/order/create");
};

// 搜索
const onSearch = () => {
  showSearchPanel.value = false;
  searchApplied.value = true;
  getTabData().pages.current = 1;
  finished.value = false;
  getList();
};

// 标签页切换
const handleTabClick = () => {
  getTabData().pages.current = 1;
  finished.value = false;
  getList();
};

// 显示确认对话框
const showConfirmDialog = (id) => {
  currentOrderId.value = id;
  confirmDialogVisible.value = true;
};

// 显示删除对话框
const showDeleteDialog = (id) => {
  currentOrderId.value = id;
  deleteDialogVisible.value = true;
};

// 确认订单
const confirmOrderAction = async () => {
  try {
    await apiService.order.confirmOrder(currentOrderId.value);
    showSuccessToast("订单确认成功");
    confirmDialogVisible.value = false;
    refreshList();
  } catch (error) {
    showToast("确认订单失败");
  }
};

// 删除订单
const deleteOrderAction = async () => {
  try {
    await apiService.order.deleteOrder(currentOrderId.value);
    showSuccessToast("订单删除成功");
    deleteDialogVisible.value = false;
    refreshList();
  } catch (error) {
    showToast("删除订单失败");
  }
};

// 查看订单详情
const edit = (item) => {
  currentOrder.value.id = item.id;
  detailVisible.value = true;
};

// 下载订单详情excel
const downloadOrderDetailExcel = (orderId) => {
  apiService.utils.loadDown(apiService.urls.orderDetailExcel + orderId);
};

// 下载装箱单excel
const downloadPackingListExcel = (logisticsNumber, orderId) => {
  apiService.utils.loadDown(
    apiService.urls.orderPackingListExcel +
      logisticsNumber +
      "&orderId=" +
      orderId
  );
};

// 下载发票PDF
const downloadInvoicePdf = (orderId) => {
  apiService.order
    .generateInvoicePdf({ orderId })
    .then((res) => {
      if (res.code === "0") {
        const fileUrl = base_downFileByPath_url + res.msg;
        // 使用移动端优化的预览函数
        const success = previewFileForMobile(fileUrl, (error) => {
          showToast("下载被阻止，请允许弹出窗口");
        });

        if (!success) {
          showToast("打开发票失败");
        }
      } else {
        showToast("获取发票失败");
      }
    })
    .catch((error) => {
      console.error("下载PDF失败:", error);
      showToast("下载失败: " + error.message);
    });
};
</script>

<style lang="scss" scoped>
.order-list-page {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 50px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  background-color: #fff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.05);

  .page-title {
    font-size: 18px;
    font-weight: 500;
    color: #323233;
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: 12px;

    .van-button {
      margin-right: 8px;
    }
  }
}

.search-panel {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  height: 100%;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 0 16px;

    .title {
      font-size: 18px;
      font-weight: 500;
      color: #323233;
    }

    .search-count {
      font-size: 14px;
      color: #666;

      .count {
        color: #ee0a24;
        font-weight: 500;
      }
    }
  }

  .search-form {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow-y: auto;
    padding-bottom: 80px;
  }

  .active-filters {
    margin: 16px;

    &-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
    }

    .filter-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .van-tag {
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }
  }

  .form-buttons {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    background-color: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  }
}

.create-order-btn {
  padding: 12px 16px;
}

.order-list {
  padding: 8px;
}

.order-item {
  margin-bottom: 12px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 12px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eee;

    .order-date {
      font-size: 13px;
      color: #666;
    }
  }

  .order-info {
    padding: 12px;

    .order-id {
      display: flex;
      align-items: center;
      font-size: 14px;
      margin-bottom: 8px;

      .van-icon {
        margin-right: 4px;
        color: #666;
      }
    }

    .order-price {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .price-label {
        font-size: 14px;
        color: #666;
      }

      .price-value {
        font-size: 16px;
        font-weight: bold;
        color: #ee0a24;
      }
    }
  }

  .logistics-info {
    padding: 0 12px 12px;
    border-bottom: 1px solid #f5f5f5;

    .logistics-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 13px;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }

      .logistics-company {
        display: flex;
        align-items: center;
        color: #666;

        .van-icon {
          margin-right: 4px;
        }
      }

      .logistics-number {
        display: flex;
        align-items: center;
        color: #1989fa;

        .van-icon {
          margin-left: 4px;
        }
      }
    }
  }

  .order-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
    gap: 8px;
    padding: 12px;
  }
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
  background-color: #f7f8fa;

  .title {
    font-size: 16px;
    font-weight: bold;
  }
}
</style> 